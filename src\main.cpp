#include "MonitorWindow.h"
#include "Utils.h"
#include <iostream>

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // 避免未使用参数的警告
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);
    
    // 初始化COM库
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) {
        Utils::ShowErrorMessage(L"无法初始化COM库");
        return -1;
    }
    
    try {
        // 创建监控窗口
        MonitorWindow window;
        
        if (!window.Create()) {
            Utils::ShowErrorMessage(L"无法创建监控窗口");
            CoUninitialize();
            return -1;
        }
        
        // 显示窗口
        window.Show();
        
        // 进入消息循环
        int result = window.MessageLoop();
        
        // 清理
        window.Destroy();
        CoUninitialize();
        
        return result;
    }
    catch (const std::exception& e) {
        std::wstring errorMsg = L"程序运行时发生错误: ";
        errorMsg += Utils::StringToWString(e.what());
        Utils::ShowErrorMessage(errorMsg);
        
        CoUninitialize();
        return -1;
    }
    catch (...) {
        Utils::ShowErrorMessage(L"程序运行时发生未知错误");
        CoUninitialize();
        return -1;
    }
}

#ifdef _DEBUG
// Debug模式下也提供控制台入口点
int main() {
    return WinMain(GetModuleHandle(nullptr), nullptr, GetCommandLineA(), SW_SHOWNORMAL);
}
#endif
