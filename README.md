# 系统状态监控悬浮窗

一个用C++开发的轻量级系统状态监控悬浮窗，实时显示CPU、内存、GPU、网速、磁盘占用率。

## 功能特性

- 🖥️ **实时监控**: CPU使用率、内存使用率、磁盘使用率
- 🌐 **网络监控**: 实时显示上传/下载速度
- 🎮 **GPU监控**: GPU使用率显示（待完善）
- 🎨 **悬浮窗口**: 透明背景、置顶显示、可拖拽
- 🔧 **系统托盘**: 托盘图标、右键菜单
- 💾 **位置记忆**: 自动保存和恢复窗口位置

## 项目结构

```
SystemE/
├── include/           # 头文件目录
│   ├── Common.h      # 公共定义和常量
│   ├── SystemInfo.h  # 系统信息获取类
│   ├── MonitorWindow.h # 监控窗口类
│   └── Utils.h       # 工具函数类
├── src/              # 源文件目录
│   ├── main.cpp      # 程序入口
│   ├── SystemInfo.cpp # 系统信息获取实现
│   ├── MonitorWindow.cpp # 监控窗口实现
│   └── Utils.cpp     # 工具函数实现
├── CMakeLists.txt    # CMake构建配置
├── Makefile          # Make构建配置
├── compile.bat       # Windows批处理编译脚本
└── README.md         # 项目说明文档
```

## 编译要求

### Windows环境
- **编译器**: Visual Studio 2019/2022 或 MinGW-w64
- **CMake**: 3.16 或更高版本（可选）
- **Windows SDK**: 包含必要的Windows API库

### 依赖库
项目使用以下Windows API库：
- `user32.lib` - 用户界面API
- `gdi32.lib` - 图形设备接口
- `kernel32.lib` - 核心系统API
- `advapi32.lib` - 高级API（注册表操作）
- `shell32.lib` - Shell API（托盘图标）
- `psapi.lib` - 进程状态API
- `pdh.lib` - 性能数据助手
- `iphlpapi.lib` - IP助手API（网络监控）
- `ws2_32.lib` - Winsock API
- `shlwapi.lib` - Shell轻量级API
- `wbemuuid.lib` - WMI API

## 编译方法

### 方法1: 使用CMake（推荐）
```bash
# 创建构建目录
mkdir build
cd build

# 生成项目文件
cmake .. -G "Visual Studio 17 2022" -A x64

# 编译项目
cmake --build . --config Release
```

### 方法2: 使用Visual Studio编译器
```cmd
# 运行compile.bat脚本
compile.bat
```

### 方法3: 使用MinGW
```bash
# 使用Make编译
make all
```

### 方法4: 手动编译
```bash
g++ -std=c++17 -Iinclude src/*.cpp -o SystemMonitor.exe \
    -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 \
    -lole32 -loleaut32 -luuid -lpsapi -lpdh -liphlpapi \
    -lws2_32 -lshlwapi -lwbemuuid
```

## 使用说明

1. **启动程序**: 运行编译生成的 `SystemMonitor.exe`
2. **查看信息**: 悬浮窗会显示实时的系统状态信息
3. **拖拽窗口**: 左键点击并拖拽可移动窗口位置
4. **右键菜单**: 右键点击窗口或托盘图标显示菜单
5. **退出程序**: 通过右键菜单选择"退出"

## 界面说明

悬浮窗显示以下信息：
- **CPU**: CPU使用率百分比和进度条
- **内存**: 内存使用率百分比和进度条
- **GPU**: GPU使用率百分比和进度条（待完善）
- **磁盘**: 系统盘使用率百分比和进度条
- **网速**: 实时上传/下载速度
- **内存信息**: 可用内存/总内存

## 技术特点

- **高性能**: 使用Windows原生API，资源占用极低
- **实时更新**: 每秒更新一次系统状态信息
- **双缓冲绘制**: 避免界面闪烁
- **透明效果**: 半透明背景，美观不遮挡
- **置顶显示**: 始终保持在最前端
- **位置记忆**: 程序重启后恢复上次窗口位置

## 已知问题

1. **GPU监控**: 当前GPU使用率显示为0，需要集成NVIDIA-ML API或AMD ADL
2. **网络接口**: 目前只监控第一个以太网接口
3. **多显示器**: 在多显示器环境下可能需要调整窗口位置逻辑

## 开发计划

- [ ] 完善GPU使用率监控
- [ ] 添加设置界面
- [ ] 支持自定义刷新间隔
- [ ] 添加更多系统信息（温度、风扇转速等）
- [ ] 支持主题切换
- [ ] 添加历史数据图表

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
