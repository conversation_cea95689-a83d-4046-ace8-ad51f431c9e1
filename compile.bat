@echo off
echo 正在编译系统监控程序...

REM 尝试多个可能的Visual Studio路径
set "VS_FOUND=0"

REM Visual Studio 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    goto :compile
)

REM Visual Studio 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    goto :compile
)

REM Visual Studio 2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    goto :compile
)

REM Visual Studio Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
    goto :compile
)

if "%VS_FOUND%"=="0" (
    echo 错误：未找到Visual Studio编译环境！
    echo 请确保已安装以下之一：
    echo - Visual Studio 2019/2022 Community/Professional
    echo - Visual Studio Build Tools
    echo 或者手动设置编译环境后运行此脚本
    pause
    exit /b 1
)

:compile
REM 创建输出目录
if not exist bin mkdir bin

REM 编译源文件
echo 编译中...
cl /EHsc /std:c++17 /I include /DUNICODE /D_UNICODE ^
   src\main.cpp src\SystemInfo.cpp src\MonitorWindow.cpp src\Utils.cpp ^
   /Fe:bin\SystemMonitor.exe ^
   /link user32.lib gdi32.lib kernel32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib winspool.lib winmm.lib psapi.lib pdh.lib iphlpapi.lib ws2_32.lib shlwapi.lib wbemuuid.lib

if %ERRORLEVEL% neq 0 (
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译成功！
echo 可执行文件位于: bin\SystemMonitor.exe
echo ========================================
echo.
pause
