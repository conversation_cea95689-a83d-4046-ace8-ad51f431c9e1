@echo off
echo 正在编译系统监控程序...

REM 设置Visual Studio环境变量
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if %ERRORLEVEL% neq 0 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %ERRORLEVEL% neq 0 (
        echo 未找到Visual Studio编译环境！
        echo 请确保已安装Visual Studio 2019或2022
        pause
        exit /b 1
    )
)

REM 创建输出目录
if not exist bin mkdir bin

REM 编译源文件
echo 编译中...
cl /EHsc /std:c++17 /I include ^
   src\main.cpp src\SystemInfo.cpp src\MonitorWindow.cpp src\Utils.cpp ^
   /Fe:bin\SystemMonitor.exe ^
   /link user32.lib gdi32.lib kernel32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib winspool.lib winmm.lib psapi.lib pdh.lib iphlpapi.lib ws2_32.lib shlwapi.lib wbemuuid.lib

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！可执行文件位于: bin\SystemMonitor.exe
pause
