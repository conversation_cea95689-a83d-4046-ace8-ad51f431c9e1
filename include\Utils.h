#pragma once

#include "Common.h"

class Utils {
public:
    // 字符串转换
    static std::wstring StringToWString(const std::string& str);
    static std::string WStringToString(const std::wstring& wstr);
    
    // 格式化函数
    static std::wstring FormatBytes(size_t bytes);
    static std::wstring FormatPercentage(double percentage);
    static std::wstring FormatSpeed(double bytesPerSecond);
    
    // 颜色工具
    static COLORREF GetUsageColor(double percentage);
    static COLORREF BlendColors(COLORREF color1, COLORREF color2, double ratio);
    
    // 注册表操作
    static bool ReadRegistryString(HKEY hKey, const std::wstring& subKey, 
                                  const std::wstring& valueName, std::wstring& result);
    static bool WriteRegistryString(HKE<PERSON> hKey, const std::wstring& subKey, 
                                   const std::wstring& valueName, const std::wstring& value);
    static bool ReadRegistryDWORD(HKEY hKey, const std::wstring& subKey, 
                                 const std::wstring& valueName, DWORD& result);
    static bool WriteRegistryDWORD(HKEY hKey, const std::wstring& subKey, 
                                  const std::wstring& valueName, DWORD value);
    
    // 文件和路径操作
    static std::wstring GetExecutablePath();
    static std::wstring GetExecutableDirectory();
    static bool FileExists(const std::wstring& filePath);
    static bool CreateDirectoryRecursive(const std::wstring& dirPath);
    
    // 系统信息辅助
    static std::string GetSystemVersion();
    static bool IsWindows10OrLater();
    static bool IsRunningAsAdmin();
    
    // 错误处理
    static std::wstring GetLastErrorString();
    static void ShowErrorMessage(const std::wstring& message, const std::wstring& title = L"错误");
    static void ShowInfoMessage(const std::wstring& message, const std::wstring& title = L"信息");
    
    // 数学工具
    static double Clamp(double value, double min, double max);
    static int Clamp(int value, int min, int max);
    static double Lerp(double a, double b, double t);
    
private:
    Utils() = delete;  // 工具类，不允许实例化
};
