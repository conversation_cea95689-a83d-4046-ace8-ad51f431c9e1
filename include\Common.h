#pragma once

#include <windows.h>
#include <string>
#include <memory>
#include <vector>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>

// 常量定义
constexpr int WINDOW_WIDTH = 280;
constexpr int WINDOW_HEIGHT = 160;
constexpr int UPDATE_INTERVAL_MS = 1000;  // 更新间隔（毫秒）
constexpr int TIMER_ID = 1;

// 颜色定义
constexpr COLORREF COLOR_BACKGROUND = RGB(20, 20, 20);
constexpr COLORREF COLOR_TEXT = RGB(255, 255, 255);
constexpr COLORREF COLOR_ACCENT = RGB(0, 120, 215);
constexpr COLORREF COLOR_WARNING = RGB(255, 193, 7);
constexpr COLORREF COLOR_DANGER = RGB(220, 53, 69);

// 系统信息结构体
struct SystemStatus {
    double cpuUsage = 0.0;
    double memoryUsage = 0.0;
    double gpuUsage = 0.0;
    double diskUsage = 0.0;
    double networkUpload = 0.0;    // KB/s
    double networkDownload = 0.0;  // KB/s
    
    std::string cpuName;
    std::string gpuName;
    size_t totalMemory = 0;        // MB
    size_t availableMemory = 0;    // MB
};

// 窗口消息定义
#define WM_TRAYICON (WM_USER + 1)
#define ID_TRAY_EXIT 1001
#define ID_TRAY_SETTINGS 1002
#define ID_TRAY_ABOUT 1003

// 工具函数声明
std::wstring StringToWString(const std::string& str);
std::string WStringToString(const std::wstring& wstr);
std::wstring FormatBytes(size_t bytes);
std::wstring FormatPercentage(double percentage);
COLORREF GetUsageColor(double percentage);
