#include "MonitorWindow.h"
#include "Utils.h"
#include <windowsx.h>
#include <shellapi.h>

#pragma comment(lib, "shell32.lib")

const wchar_t* WINDOW_CLASS_NAME = L"SystemMonitorWindow";

MonitorWindow::MonitorWindow()
    : m_hWnd(nullptr)
    , m_hInstance(GetModuleHandle(nullptr))
    , m_pSystemInfo(std::make_unique<SystemInfo>())
    , m_bDragging(false)
    , m_bTrayIconCreated(false)
    , m_hFont(nullptr)
    , m_hBoldFont(nullptr)
    , m_hBackgroundBrush(nullptr)
    , m_bVisible(false)
    , m_bUpdateEnabled(true) {
    
    ZeroMemory(&m_nid, sizeof(m_nid));
    ZeroMemory(&m_dragStartPoint, sizeof(m_dragStartPoint));
    ZeroMemory(&m_windowStartPoint, sizeof(m_windowStartPoint));
}

MonitorWindow::~MonitorWindow() {
    Destroy();
}

bool MonitorWindow::Create() {
    // 注册窗口类
    RegisterWindowClass();
    
    // 初始化系统信息
    if (!m_pSystemInfo->Initialize()) {
        Utils::ShowErrorMessage(L"无法初始化系统信息监控");
        return false;
    }
    
    // 创建窗口
    m_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        WINDOW_CLASS_NAME,
        L"系统监控",
        WS_POPUP,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        nullptr, nullptr, m_hInstance, this
    );
    
    if (!m_hWnd) {
        Utils::ShowErrorMessage(L"无法创建窗口: " + Utils::GetLastErrorString());
        return false;
    }
    
    // 设置窗口透明度
    SetLayeredWindowAttributes(m_hWnd, 0, 240, LWA_ALPHA);
    
    // 初始化资源
    if (!InitializeResources()) {
        return false;
    }
    
    // 创建托盘图标
    CreateTrayIcon();
    
    // 加载窗口位置
    LoadWindowPosition();
    
    // 启动定时器
    SetTimer(m_hWnd, TIMER_ID, UPDATE_INTERVAL_MS, nullptr);
    
    return true;
}

void MonitorWindow::Show() {
    if (m_hWnd) {
        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);
        m_bVisible = true;
    }
}

void MonitorWindow::Hide() {
    if (m_hWnd) {
        ShowWindow(m_hWnd, SW_HIDE);
        m_bVisible = false;
    }
}

void MonitorWindow::Destroy() {
    if (m_hWnd) {
        KillTimer(m_hWnd, TIMER_ID);
        DestroyTrayIcon();
        CleanupResources();
        DestroyWindow(m_hWnd);
        m_hWnd = nullptr;
    }
    
    if (m_pSystemInfo) {
        m_pSystemInfo->Cleanup();
    }
}

int MonitorWindow::MessageLoop() {
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    return static_cast<int>(msg.wParam);
}

void MonitorWindow::RegisterWindowClass() {
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = m_hInstance;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = nullptr; // 我们自己绘制背景
    wc.lpszClassName = WINDOW_CLASS_NAME;
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);
    
    RegisterClassEx(&wc);
}

bool MonitorWindow::InitializeResources() {
    // 创建字体
    m_hFont = CreateFont(
        14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI"
    );
    
    m_hBoldFont = CreateFont(
        14, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI"
    );
    
    // 创建背景画刷
    m_hBackgroundBrush = CreateSolidBrush(COLOR_BACKGROUND);
    
    return m_hFont && m_hBoldFont && m_hBackgroundBrush;
}

void MonitorWindow::CleanupResources() {
    if (m_hFont) {
        DeleteObject(m_hFont);
        m_hFont = nullptr;
    }
    
    if (m_hBoldFont) {
        DeleteObject(m_hBoldFont);
        m_hBoldFont = nullptr;
    }
    
    if (m_hBackgroundBrush) {
        DeleteObject(m_hBackgroundBrush);
        m_hBackgroundBrush = nullptr;
    }
}

bool MonitorWindow::CreateTrayIcon() {
    m_nid.cbSize = sizeof(NOTIFYICONDATA);
    m_nid.hWnd = m_hWnd;
    m_nid.uID = 1;
    m_nid.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP;
    m_nid.uCallbackMessage = WM_TRAYICON;
    m_nid.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wcscpy_s(m_nid.szTip, L"系统状态监控");
    
    m_bTrayIconCreated = Shell_NotifyIcon(NIM_ADD, &m_nid);
    return m_bTrayIconCreated;
}

void MonitorWindow::DestroyTrayIcon() {
    if (m_bTrayIconCreated) {
        Shell_NotifyIcon(NIM_DELETE, &m_nid);
        m_bTrayIconCreated = false;
    }
}

LRESULT CALLBACK MonitorWindow::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    MonitorWindow* pWindow = nullptr;
    
    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pWindow = reinterpret_cast<MonitorWindow*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pWindow));
    } else {
        pWindow = reinterpret_cast<MonitorWindow*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    if (pWindow) {
        switch (uMsg) {
            case WM_CREATE:
                return pWindow->HandleCreate();
            case WM_DESTROY:
                return pWindow->HandleDestroy();
            case WM_PAINT:
                return pWindow->HandlePaint();
            case WM_TIMER:
                return pWindow->HandleTimer(wParam);
            case WM_LBUTTONDOWN:
                return pWindow->HandleMouseDown(wParam, lParam);
            case WM_MOUSEMOVE:
                return pWindow->HandleMouseMove(wParam, lParam);
            case WM_LBUTTONUP:
                return pWindow->HandleMouseUp(wParam, lParam);
            case WM_RBUTTONUP:
                return pWindow->HandleRightClick(lParam);
            case WM_TRAYICON:
                return pWindow->HandleTrayIcon(wParam, lParam);
            case WM_COMMAND:
                return pWindow->HandleCommand(wParam);
        }
    }
    
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

LRESULT MonitorWindow::HandleCreate() {
    return 0;
}

LRESULT MonitorWindow::HandleDestroy() {
    SaveWindowPosition();
    PostQuitMessage(0);
    return 0;
}

LRESULT MonitorWindow::HandlePaint() {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(m_hWnd, &ps);

    // 创建内存DC进行双缓冲绘制
    HDC hdcMem = CreateCompatibleDC(hdc);
    HBITMAP hbmMem = CreateCompatibleBitmap(hdc, WINDOW_WIDTH, WINDOW_HEIGHT);
    HBITMAP hbmOld = static_cast<HBITMAP>(SelectObject(hdcMem, hbmMem));

    // 填充背景
    RECT rect = { 0, 0, WINDOW_WIDTH, WINDOW_HEIGHT };
    FillRect(hdcMem, &rect, m_hBackgroundBrush);

    // 绘制系统信息
    DrawSystemInfo(hdcMem);

    // 复制到屏幕
    BitBlt(hdc, 0, 0, WINDOW_WIDTH, WINDOW_HEIGHT, hdcMem, 0, 0, SRCCOPY);

    // 清理
    SelectObject(hdcMem, hbmOld);
    DeleteObject(hbmMem);
    DeleteDC(hdcMem);

    EndPaint(m_hWnd, &ps);
    return 0;
}

LRESULT MonitorWindow::HandleTimer(WPARAM wParam) {
    if (wParam == TIMER_ID && m_bUpdateEnabled) {
        UpdateSystemInfo();
        InvalidateRect(m_hWnd, nullptr, FALSE);
    }
    return 0;
}

LRESULT MonitorWindow::HandleMouseDown(WPARAM wParam, LPARAM lParam) {
    if (wParam & MK_LBUTTON) {
        m_bDragging = true;
        m_dragStartPoint.x = GET_X_LPARAM(lParam);
        m_dragStartPoint.y = GET_Y_LPARAM(lParam);

        RECT windowRect;
        GetWindowRect(m_hWnd, &windowRect);
        m_windowStartPoint.x = windowRect.left;
        m_windowStartPoint.y = windowRect.top;

        SetCapture(m_hWnd);
    }
    return 0;
}

LRESULT MonitorWindow::HandleMouseMove(WPARAM wParam, LPARAM lParam) {
    if (m_bDragging && (wParam & MK_LBUTTON)) {
        int currentX = GET_X_LPARAM(lParam);
        int currentY = GET_Y_LPARAM(lParam);

        int deltaX = currentX - m_dragStartPoint.x;
        int deltaY = currentY - m_dragStartPoint.y;

        int newX = m_windowStartPoint.x + deltaX;
        int newY = m_windowStartPoint.y + deltaY;

        SetWindowPosition(newX, newY);
    }
    return 0;
}

LRESULT MonitorWindow::HandleMouseUp(WPARAM wParam, LPARAM lParam) {
    UNREFERENCED_PARAMETER(wParam);
    UNREFERENCED_PARAMETER(lParam);

    if (m_bDragging) {
        m_bDragging = false;
        ReleaseCapture();
    }
    return 0;
}

LRESULT MonitorWindow::HandleRightClick(LPARAM lParam) {
    POINT pt;
    pt.x = GET_X_LPARAM(lParam);
    pt.y = GET_Y_LPARAM(lParam);
    ClientToScreen(m_hWnd, &pt);

    ShowContextMenu(pt.x, pt.y);
    return 0;
}

LRESULT MonitorWindow::HandleTrayIcon(WPARAM wParam, LPARAM lParam) {
    UNREFERENCED_PARAMETER(wParam);

    switch (lParam) {
        case WM_LBUTTONDBLCLK:
            if (m_bVisible) {
                Hide();
            } else {
                Show();
            }
            break;

        case WM_RBUTTONUP:
            POINT pt;
            GetCursorPos(&pt);
            ShowContextMenu(pt.x, pt.y);
            break;
    }
    return 0;
}

LRESULT MonitorWindow::HandleCommand(WPARAM wParam) {
    switch (LOWORD(wParam)) {
        case ID_TRAY_EXIT:
            PostMessage(m_hWnd, WM_CLOSE, 0, 0);
            break;

        case ID_TRAY_SETTINGS:
            // 这里可以添加设置对话框
            Utils::ShowInfoMessage(L"设置功能待实现");
            break;

        case ID_TRAY_ABOUT:
            Utils::ShowInfoMessage(L"系统状态监控 v1.0\n\n实时显示CPU、内存、GPU、网速、磁盘使用率", L"关于");
            break;
    }
    return 0;
}

void MonitorWindow::ShowContextMenu(int x, int y) {
    HMENU hMenu = CreatePopupMenu();

    AppendMenu(hMenu, MF_STRING, ID_TRAY_SETTINGS, L"设置");
    AppendMenu(hMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hMenu, MF_STRING, ID_TRAY_ABOUT, L"关于");
    AppendMenu(hMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hMenu, MF_STRING, ID_TRAY_EXIT, L"退出");

    // 设置前台窗口以确保菜单能正确显示
    SetForegroundWindow(m_hWnd);

    TrackPopupMenu(hMenu, TPM_RIGHTBUTTON, x, y, 0, m_hWnd, nullptr);

    DestroyMenu(hMenu);
}

void MonitorWindow::UpdateSystemInfo() {
    if (m_pSystemInfo) {
        m_currentStatus = m_pSystemInfo->GetSystemStatus();
    }
}

void MonitorWindow::SetWindowPosition(int x, int y) {
    SetWindowPos(m_hWnd, nullptr, x, y, 0, 0, SWP_NOSIZE | SWP_NOZORDER);
}

void MonitorWindow::SaveWindowPosition() {
    if (m_hWnd) {
        RECT rect;
        GetWindowRect(m_hWnd, &rect);

        Utils::WriteRegistryDWORD(HKEY_CURRENT_USER, L"Software\\SystemMonitor", L"WindowX", rect.left);
        Utils::WriteRegistryDWORD(HKEY_CURRENT_USER, L"Software\\SystemMonitor", L"WindowY", rect.top);
    }
}

void MonitorWindow::LoadWindowPosition() {
    DWORD x, y;
    if (Utils::ReadRegistryDWORD(HKEY_CURRENT_USER, L"Software\\SystemMonitor", L"WindowX", x) &&
        Utils::ReadRegistryDWORD(HKEY_CURRENT_USER, L"Software\\SystemMonitor", L"WindowY", y)) {
        SetWindowPosition(static_cast<int>(x), static_cast<int>(y));
    }
}

void MonitorWindow::DrawSystemInfo(HDC hdc) {
    SetBkMode(hdc, TRANSPARENT);

    int y = 10;
    const int lineHeight = 20;
    const int progressBarWidth = 120;
    const int progressBarHeight = 8;

    // CPU使用率
    DrawText(hdc, L"CPU:", 10, y, COLOR_TEXT, true);
    DrawText(hdc, Utils::FormatPercentage(m_currentStatus.cpuUsage), 50, y, COLOR_TEXT);
    DrawProgressBar(hdc, 120, y + 6, progressBarWidth, progressBarHeight,
                   m_currentStatus.cpuUsage, Utils::GetUsageColor(m_currentStatus.cpuUsage));
    y += lineHeight;

    // 内存使用率
    DrawText(hdc, L"内存:", 10, y, COLOR_TEXT, true);
    DrawText(hdc, Utils::FormatPercentage(m_currentStatus.memoryUsage), 50, y, COLOR_TEXT);
    DrawProgressBar(hdc, 120, y + 6, progressBarWidth, progressBarHeight,
                   m_currentStatus.memoryUsage, Utils::GetUsageColor(m_currentStatus.memoryUsage));
    y += lineHeight;

    // GPU使用率
    DrawText(hdc, L"GPU:", 10, y, COLOR_TEXT, true);
    DrawText(hdc, Utils::FormatPercentage(m_currentStatus.gpuUsage), 50, y, COLOR_TEXT);
    DrawProgressBar(hdc, 120, y + 6, progressBarWidth, progressBarHeight,
                   m_currentStatus.gpuUsage, Utils::GetUsageColor(m_currentStatus.gpuUsage));
    y += lineHeight;

    // 磁盘使用率
    DrawText(hdc, L"磁盘:", 10, y, COLOR_TEXT, true);
    DrawText(hdc, Utils::FormatPercentage(m_currentStatus.diskUsage), 50, y, COLOR_TEXT);
    DrawProgressBar(hdc, 120, y + 6, progressBarWidth, progressBarHeight,
                   m_currentStatus.diskUsage, Utils::GetUsageColor(m_currentStatus.diskUsage));
    y += lineHeight;

    // 网络速度
    DrawText(hdc, L"网速:", 10, y, COLOR_TEXT, true);
    std::wstring networkText = L"↑" + Utils::FormatSpeed(m_currentStatus.networkUpload * 1024) +
                              L" ↓" + Utils::FormatSpeed(m_currentStatus.networkDownload * 1024);
    DrawText(hdc, networkText, 50, y, COLOR_TEXT);
    y += lineHeight;

    // 内存信息
    if (m_currentStatus.totalMemory > 0) {
        std::wstring memInfo = Utils::FormatBytes(m_currentStatus.availableMemory * 1024 * 1024) +
                              L" / " + Utils::FormatBytes(m_currentStatus.totalMemory * 1024 * 1024);
        DrawText(hdc, memInfo, 10, y, RGB(180, 180, 180));
    }
}

void MonitorWindow::DrawProgressBar(HDC hdc, int x, int y, int width, int height, double percentage, COLORREF color) {
    // 绘制背景
    RECT bgRect = { x, y, x + width, y + height };
    HBRUSH hBgBrush = CreateSolidBrush(RGB(60, 60, 60));
    FillRect(hdc, &bgRect, hBgBrush);
    DeleteObject(hBgBrush);

    // 绘制进度
    if (percentage > 0) {
        int progressWidth = static_cast<int>((width * percentage) / 100.0);
        progressWidth = Utils::Clamp(progressWidth, 0, width);

        RECT progressRect = { x, y, x + progressWidth, y + height };
        HBRUSH hProgressBrush = CreateSolidBrush(color);
        FillRect(hdc, &progressRect, hProgressBrush);
        DeleteObject(hProgressBrush);
    }

    // 绘制边框
    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(100, 100, 100));
    HPEN hOldPen = static_cast<HPEN>(SelectObject(hdc, hPen));

    MoveToEx(hdc, x, y, nullptr);
    LineTo(hdc, x + width, y);
    LineTo(hdc, x + width, y + height);
    LineTo(hdc, x, y + height);
    LineTo(hdc, x, y);

    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);
}

void MonitorWindow::DrawText(HDC hdc, const std::wstring& text, int x, int y, COLORREF color, bool bold) {
    HFONT hOldFont = static_cast<HFONT>(SelectObject(hdc, bold ? m_hBoldFont : m_hFont));
    COLORREF oldColor = SetTextColor(hdc, color);

    TextOut(hdc, x, y, text.c_str(), static_cast<int>(text.length()));

    SetTextColor(hdc, oldColor);
    SelectObject(hdc, hOldFont);
}
