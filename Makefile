# Makefile for SystemMonitor

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -Iinclude
LDFLAGS = -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -lwinspool -lwinmm -lpsapi -lpdh -liphlpapi -lws2_32 -lshlwapi

SRCDIR = src
INCDIR = include
OBJDIR = obj
BINDIR = bin

SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/SystemMonitor.exe

.PHONY: all clean test

all: $(TARGET)

$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CXX) $(OBJECTS) -o $@ $(LDFLAGS)

$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

test: test_compile.exe
	./test_compile.exe

test_compile.exe: test_compile.cpp
	$(CXX) -o $@ $<

clean:
	rm -rf $(OBJDIR) $(BINDIR) test_compile.exe

install: $(TARGET)
	@echo "SystemMonitor compiled successfully!"
	@echo "Executable: $(TARGET)"
