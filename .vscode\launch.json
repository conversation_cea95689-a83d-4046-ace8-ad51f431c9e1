{"version": "0.2.0", "configurations": [{"name": "Debug (MinGW)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/SystemMonitor.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build-mingw"}, {"name": "Debug (MSVC)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/bin/SystemMonitor.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "preLaunchTask": "build-msvc"}]}