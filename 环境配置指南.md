# C++ 编译环境配置指南

## 当前问题
你的系统中缺少C++编译器。Visual Studio Code只是一个代码编辑器，需要单独安装编译器才能编译C++代码。

## 解决方案

### 方案1：安装MinGW-w64（推荐，简单快速）

1. **下载MinGW-w64**
   - 访问：https://www.mingw-w64.org/downloads/
   - 或者直接下载：https://github.com/niXman/mingw-builds-binaries/releases
   - 选择最新版本的 `x86_64-posix-seh` 版本

2. **安装步骤**
   - 下载后解压到 `C:\mingw64`
   - 将 `C:\mingw64\bin` 添加到系统PATH环境变量

3. **验证安装**
   ```cmd
   gcc --version
   g++ --version
   ```

### 方案2：安装Visual Studio Community（功能完整）

1. **下载Visual Studio Community**
   - 访问：https://visualstudio.microsoft.com/zh-hans/vs/community/
   - 下载并运行安装程序

2. **选择工作负载**
   - 勾选"使用C++的桌面开发"
   - 确保包含MSVC编译器和Windows SDK

3. **验证安装**
   - 打开"Developer Command Prompt for VS"
   - 运行 `cl` 命令

### 方案3：使用MSYS2（类Unix环境）

1. **下载MSYS2**
   - 访问：https://www.msys2.org/
   - 下载并安装

2. **安装编译器**
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-cmake
   pacman -S mingw-w64-x86_64-make
   ```

## 配置Visual Studio Code

安装编译器后，在VS Code中安装以下扩展：

1. **C/C++ Extension Pack**
   - 包含C/C++、CMake Tools等扩展

2. **配置tasks.json**
   创建 `.vscode/tasks.json`：
   ```json
   {
       "version": "2.0.0",
       "tasks": [
           {
               "label": "build",
               "type": "shell",
               "command": "g++",
               "args": [
                   "-std=c++17",
                   "-Iinclude",
                   "src/*.cpp",
                   "-o",
                   "bin/SystemMonitor.exe",
                   "-luser32",
                   "-lgdi32",
                   "-lkernel32",
                   "-ladvapi32",
                   "-lshell32",
                   "-lpsapi",
                   "-lpdh",
                   "-liphlpapi",
                   "-lws2_32",
                   "-lshlwapi"
               ],
               "group": {
                   "kind": "build",
                   "isDefault": true
               },
               "presentation": {
                   "echo": true,
                   "reveal": "always",
                   "focus": false,
                   "panel": "shared"
               }
           }
       ]
   }
   ```

## 快速测试

安装编译器后，运行以下命令测试：

```bash
# 创建bin目录
mkdir bin

# 编译项目
g++ -std=c++17 -Iinclude src/*.cpp -o bin/SystemMonitor.exe -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lpsapi -lpdh -liphlpapi -lws2_32 -lshlwapi

# 运行程序
./bin/SystemMonitor.exe
```

## 推荐方案

**建议选择方案1（MinGW-w64）**，因为：
- 安装简单快速
- 占用空间小
- 与我们的项目完全兼容
- 支持最新的C++17标准

安装完成后，请告诉我，我会帮你测试编译！
