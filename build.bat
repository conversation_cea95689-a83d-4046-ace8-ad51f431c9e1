@echo off
echo 正在构建系统监控程序...

REM 创建构建目录
if not exist build mkdir build
cd build

REM 使用CMake生成项目文件
echo 生成项目文件...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

REM 编译项目
echo 编译项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 构建完成！可执行文件位于: build\bin\Release\SystemMonitor.exe
pause
