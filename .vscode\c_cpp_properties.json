{"version": 4, "configurations": [{"name": "Win32-MinG<PERSON>", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "_WIN32", "_WIN32_WINNT=0x0A00"], "compilerPath": "C:/mingw64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64"}, {"name": "Win32-MSVC", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "_WIN32", "_WIN32_WINNT=0x0A00"], "compilerPath": "cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}]}