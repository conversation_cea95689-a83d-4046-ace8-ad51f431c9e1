#include <windows.h>
#include <iostream>
#include <string>

// 简单测试程序，验证基本的Windows API编译
int main() {
    std::wcout << L"测试编译成功！" << std::endl;
    
    // 测试一些基本的Windows API
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    
    if (GlobalMemoryStatusEx(&memInfo)) {
        std::wcout << L"内存使用率: " << memInfo.dwMemoryLoad << L"%" << std::endl;
        std::wcout << L"总内存: " << (memInfo.ullTotalPhys / (1024 * 1024)) << L" MB" << std::endl;
    }
    
    return 0;
}
