cmake_minimum_required(VERSION 3.16)
project(SystemMonitor)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 包含头文件目录
include_directories(include)

# 源文件
set(SOURCES
    src/main.cpp
    src/SystemInfo.cpp
    src/MonitorWindow.cpp
    src/Utils.cpp
)

# 头文件
set(HEADERS
    include/SystemInfo.h
    include/MonitorWindow.h
    include/Utils.h
    include/Common.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Windows特定设置
if(WIN32)
    # 链接Windows API库
    target_link_libraries(${PROJECT_NAME} 
        user32 
        gdi32 
        kernel32 
        advapi32 
        shell32 
        ole32 
        oleaut32 
        uuid 
        comdlg32 
        winspool 
        winmm 
        psapi 
        pdh 
        iphlpapi 
        ws2_32
    )
    
    # 设置为Windows应用程序（不显示控制台）
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # 添加资源文件（如果有的话）
    # target_sources(${PROJECT_NAME} PRIVATE resources/app.rc)
endif()

# 编译选项
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Debug模式下的额外设置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
endif()
