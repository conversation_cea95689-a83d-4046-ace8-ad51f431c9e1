#pragma once

#include "Common.h"
#include "SystemInfo.h"

class MonitorWindow {
public:
    MonitorWindow();
    ~MonitorWindow();
    
    bool Create();
    void Show();
    void Hide();
    void Destroy();
    
    // 消息循环
    int MessageLoop();
    
    // 窗口过程
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
private:
    HWND m_hWnd;
    HINSTANCE m_hInstance;
    std::unique_ptr<SystemInfo> m_pSystemInfo;
    SystemStatus m_currentStatus;
    
    // 拖拽相关
    bool m_bDragging;
    POINT m_dragStartPoint;
    POINT m_windowStartPoint;
    
    // 托盘图标
    NOTIFYICONDATA m_nid;
    bool m_bTrayIconCreated;
    
    // 字体和绘图资源
    HFONT m_hFont;
    HFONT m_hBoldFont;
    HBRUSH m_hBackgroundBrush;
    
    // 窗口状态
    bool m_bVisible;
    std::atomic<bool> m_bUpdateEnabled;
    
    // 消息处理函数
    LRESULT HandleCreate();
    LRESULT HandleDestroy();
    LRESULT HandlePaint();
    LRESULT HandleTimer(WPARAM wParam);
    LRESULT HandleMouseDown(WPARAM wParam, LPARAM lParam);
    LRESULT HandleMouseMove(WPARAM wParam, LPARAM lParam);
    LRESULT HandleMouseUp(WPARAM wParam, LPARAM lParam);
    LRESULT HandleRightClick(LPARAM lParam);
    LRESULT HandleTrayIcon(WPARAM wParam, LPARAM lParam);
    LRESULT HandleCommand(WPARAM wParam);
    
    // 绘图函数
    void DrawSystemInfo(HDC hdc);
    void DrawProgressBar(HDC hdc, int x, int y, int width, int height, double percentage, COLORREF color);
    void DrawText(HDC hdc, const std::wstring& text, int x, int y, COLORREF color, bool bold = false);
    
    // 托盘相关
    bool CreateTrayIcon();
    void DestroyTrayIcon();
    void ShowContextMenu(int x, int y);
    
    // 初始化和清理
    bool InitializeResources();
    void CleanupResources();
    void RegisterWindowClass();
    void UpdateSystemInfo();
    
    // 窗口位置和大小
    void SetWindowPosition(int x, int y);
    void SaveWindowPosition();
    void LoadWindowPosition();
};
