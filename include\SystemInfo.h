#pragma once

#include "Common.h"
#include <pdh.h>
#include <psapi.h>
#include <iphlpapi.h>

class SystemInfo {
public:
    SystemInfo();
    ~SystemInfo();
    
    bool Initialize();
    void Cleanup();
    
    // 获取系统状态信息
    SystemStatus GetSystemStatus();
    
    // 单独获取各项信息
    double GetCPUUsage();
    double GetMemoryUsage();
    double GetGPUUsage();
    double GetDiskUsage();
    void GetNetworkSpeed(double& uploadSpeed, double& downloadSpeed);
    
    // 获取系统信息
    std::string GetCPUName();
    std::string GetGPUName();
    void GetMemoryInfo(size_t& total, size_t& available);

private:
    // PDH相关
    PDH_HQUERY m_hQuery;
    PDH_HCOUNTER m_hCpuCounter;
    PDH_HCOUNTER m_hMemoryCounter;
    
    // 网络监控
    DWORD m_dwLastInOctets;
    DWORD m_dwLastOutOctets;
    std::chrono::steady_clock::time_point m_lastNetworkTime;
    bool m_bFirstNetworkRead;
    
    // GPU监控（通过WMI或其他方式）
    bool m_bGpuMonitorAvailable;
    
    // 磁盘监控
    std::wstring m_systemDrive;
    
    // 初始化各个监控组件
    bool InitializePDH();
    bool InitializeNetworkMonitor();
    bool InitializeGPUMonitor();
    
    // 辅助函数
    double GetDiskUsageForDrive(const std::wstring& drive);
    void UpdateNetworkCounters();
    std::string GetCPUNameFromRegistry();
    std::string GetGPUNameFromWMI();
};
