@echo off
echo ========================================
echo 系统监控程序 - 环境配置检查
echo ========================================
echo.

REM 检查是否有编译器
echo 正在检查编译环境...
echo.

REM 检查MinGW
where g++ >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [✓] 找到 MinGW g++ 编译器
    g++ --version | findstr "g++"
    echo.
    goto :test_compile
) else (
    echo [✗] 未找到 MinGW g++ 编译器
)

REM 检查MSVC
where cl >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [✓] 找到 MSVC cl 编译器
    cl 2>&1 | findstr "Microsoft"
    echo.
    goto :test_compile
) else (
    echo [✗] 未找到 MSVC cl 编译器
)

REM 检查CMake
where cmake >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [✓] 找到 CMake
    cmake --version | findstr "cmake"
    echo.
) else (
    echo [✗] 未找到 CMake
)

echo.
echo ========================================
echo 环境配置建议
echo ========================================
echo.
echo 您的系统中没有找到C++编译器。
echo 请选择以下方案之一：
echo.
echo 1. 安装 MinGW-w64 (推荐)
echo    - 下载: https://github.com/niXman/mingw-builds-binaries/releases
echo    - 解压到: C:\mingw64
echo    - 添加到PATH: C:\mingw64\bin
echo.
echo 2. 安装 Visual Studio Community
echo    - 下载: https://visualstudio.microsoft.com/zh-hans/vs/community/
echo    - 选择"使用C++的桌面开发"工作负载
echo.
echo 3. 使用在线编译器 (临时方案)
echo    - 可以使用 repl.it 或 CodePen 等在线平台
echo.
echo 安装完成后，请重新运行此脚本进行验证。
echo.
pause
exit /b 1

:test_compile
echo ========================================
echo 测试编译
echo ========================================
echo.

REM 创建bin目录
if not exist bin mkdir bin

REM 尝试编译测试程序
echo 正在编译测试程序...
where g++ >nul 2>&1
if %ERRORLEVEL% equ 0 (
    g++ -std=c++17 -Iinclude src/main.cpp src/SystemInfo.cpp src/MonitorWindow.cpp src/Utils.cpp -o bin/SystemMonitor.exe -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lole32 -loleaut32 -luuid -lpsapi -lpdh -liphlpapi -lws2_32 -lshlwapi 2>compile_error.log
    if %ERRORLEVEL% equ 0 (
        echo [✓] 编译成功！
        echo 可执行文件: bin\SystemMonitor.exe
        echo.
        echo 是否现在运行程序? (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start bin\SystemMonitor.exe
        )
    ) else (
        echo [✗] 编译失败，请检查 compile_error.log 文件
        type compile_error.log
    )
) else (
    where cl >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo 使用MSVC编译器...
        echo 请先运行 vcvars64.bat 设置环境变量
    )
)

echo.
echo ========================================
echo 配置完成
echo ========================================
pause
