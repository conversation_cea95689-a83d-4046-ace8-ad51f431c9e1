#include "Utils.h"
#include <codecvt>
#include <locale>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <shlwapi.h>

#pragma comment(lib, "shlwapi.lib")

std::wstring Utils::StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

std::string Utils::WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring Utils::FormatBytes(size_t bytes) {
    const wchar_t* units[] = { L"B", L"KB", L"MB", L"GB", L"TB" };
    int unitIndex = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unitIndex < 4) {
        size /= 1024.0;
        unitIndex++;
    }
    
    std::wostringstream oss;
    oss << std::fixed << std::setprecision(1) << size << L" " << units[unitIndex];
    return oss.str();
}

std::wstring Utils::FormatPercentage(double percentage) {
    std::wostringstream oss;
    oss << std::fixed << std::setprecision(1) << percentage << L"%";
    return oss.str();
}

std::wstring Utils::FormatSpeed(double bytesPerSecond) {
    const wchar_t* units[] = { L"B/s", L"KB/s", L"MB/s", L"GB/s" };
    int unitIndex = 0;
    double speed = bytesPerSecond;
    
    while (speed >= 1024.0 && unitIndex < 3) {
        speed /= 1024.0;
        unitIndex++;
    }
    
    std::wostringstream oss;
    oss << std::fixed << std::setprecision(1) << speed << L" " << units[unitIndex];
    return oss.str();
}

COLORREF Utils::GetUsageColor(double percentage) {
    if (percentage < 50.0) {
        return RGB(40, 167, 69);   // 绿色
    } else if (percentage < 80.0) {
        return RGB(255, 193, 7);   // 黄色
    } else {
        return RGB(220, 53, 69);   // 红色
    }
}

COLORREF Utils::BlendColors(COLORREF color1, COLORREF color2, double ratio) {
    ratio = Clamp(ratio, 0.0, 1.0);
    
    int r1 = GetRValue(color1);
    int g1 = GetGValue(color1);
    int b1 = GetBValue(color1);
    
    int r2 = GetRValue(color2);
    int g2 = GetGValue(color2);
    int b2 = GetBValue(color2);
    
    int r = static_cast<int>(r1 + (r2 - r1) * ratio);
    int g = static_cast<int>(g1 + (g2 - g1) * ratio);
    int b = static_cast<int>(b1 + (b2 - b1) * ratio);
    
    return RGB(r, g, b);
}

bool Utils::ReadRegistryString(HKEY hKey, const std::wstring& subKey, 
                              const std::wstring& valueName, std::wstring& result) {
    HKEY hSubKey;
    LONG lResult = RegOpenKeyEx(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey);
    if (lResult != ERROR_SUCCESS) {
        return false;
    }
    
    DWORD dwType = REG_SZ;
    DWORD dwSize = 0;
    lResult = RegQueryValueEx(hSubKey, valueName.c_str(), nullptr, &dwType, nullptr, &dwSize);
    if (lResult != ERROR_SUCCESS || dwType != REG_SZ) {
        RegCloseKey(hSubKey);
        return false;
    }
    
    std::vector<wchar_t> buffer(dwSize / sizeof(wchar_t));
    lResult = RegQueryValueEx(hSubKey, valueName.c_str(), nullptr, &dwType, 
                             reinterpret_cast<LPBYTE>(buffer.data()), &dwSize);
    
    RegCloseKey(hSubKey);
    
    if (lResult == ERROR_SUCCESS) {
        result = std::wstring(buffer.data());
        return true;
    }
    
    return false;
}

bool Utils::WriteRegistryString(HKEY hKey, const std::wstring& subKey, 
                               const std::wstring& valueName, const std::wstring& value) {
    HKEY hSubKey;
    LONG lResult = RegCreateKeyEx(hKey, subKey.c_str(), 0, nullptr, REG_OPTION_NON_VOLATILE,
                                 KEY_WRITE, nullptr, &hSubKey, nullptr);
    if (lResult != ERROR_SUCCESS) {
        return false;
    }
    
    lResult = RegSetValueEx(hSubKey, valueName.c_str(), 0, REG_SZ,
                           reinterpret_cast<const BYTE*>(value.c_str()),
                           static_cast<DWORD>((value.length() + 1) * sizeof(wchar_t)));
    
    RegCloseKey(hSubKey);
    return lResult == ERROR_SUCCESS;
}

double Utils::Clamp(double value, double min, double max) {
    return std::max(min, std::min(max, value));
}

int Utils::Clamp(int value, int min, int max) {
    return std::max(min, std::min(max, value));
}

double Utils::Lerp(double a, double b, double t) {
    return a + t * (b - a);
}

std::wstring Utils::GetLastErrorString() {
    DWORD errorCode = GetLastError();
    if (errorCode == 0) {
        return L"无错误";
    }
    
    LPWSTR messageBuffer = nullptr;
    size_t size = FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                               nullptr, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                               (LPWSTR)&messageBuffer, 0, nullptr);
    
    std::wstring message(messageBuffer, size);
    LocalFree(messageBuffer);
    
    return message;
}

void Utils::ShowErrorMessage(const std::wstring& message, const std::wstring& title) {
    MessageBox(nullptr, message.c_str(), title.c_str(), MB_OK | MB_ICONERROR);
}

void Utils::ShowInfoMessage(const std::wstring& message, const std::wstring& title) {
    MessageBox(nullptr, message.c_str(), title.c_str(), MB_OK | MB_ICONINFORMATION);
}

bool Utils::ReadRegistryDWORD(HKEY hKey, const std::wstring& subKey,
                             const std::wstring& valueName, DWORD& result) {
    HKEY hSubKey;
    LONG lResult = RegOpenKeyEx(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey);
    if (lResult != ERROR_SUCCESS) {
        return false;
    }

    DWORD dwType = REG_DWORD;
    DWORD dwSize = sizeof(DWORD);
    lResult = RegQueryValueEx(hSubKey, valueName.c_str(), nullptr, &dwType,
                             reinterpret_cast<LPBYTE>(&result), &dwSize);

    RegCloseKey(hSubKey);
    return lResult == ERROR_SUCCESS && dwType == REG_DWORD;
}

bool Utils::WriteRegistryDWORD(HKEY hKey, const std::wstring& subKey,
                               const std::wstring& valueName, DWORD value) {
    HKEY hSubKey;
    LONG lResult = RegCreateKeyEx(hKey, subKey.c_str(), 0, nullptr, REG_OPTION_NON_VOLATILE,
                                 KEY_WRITE, nullptr, &hSubKey, nullptr);
    if (lResult != ERROR_SUCCESS) {
        return false;
    }

    lResult = RegSetValueEx(hSubKey, valueName.c_str(), 0, REG_DWORD,
                           reinterpret_cast<const BYTE*>(&value), sizeof(DWORD));

    RegCloseKey(hSubKey);
    return lResult == ERROR_SUCCESS;
}
